import { body, param, query, validationResult } from 'express-validator'

// Validation result handler
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    })
  }
  next()
}

// Auth validations
const validateRegister = [
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('username')
    .isLength({ min: 3, max: 30 })
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username must be 3-30 characters and contain only letters, numbers, and underscores'),
  body('password')
    .isLength({ min: 8 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must be at least 8 characters with at least one lowercase, uppercase, and number'),
  body('firstName').optional().isLength({ min: 1, max: 50 }).withMessage('First name must be 1-50 characters'),
  body('lastName').optional().isLength({ min: 1, max: 50 }).withMessage('Last name must be 1-50 characters'),
  handleValidationErrors
]

const validateLogin = [
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('password').notEmpty().withMessage('Password is required'),
  handleValidationErrors
]

// User validations
const validateCreateUser = [
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('username')
    .isLength({ min: 3, max: 30 })
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username must be 3-30 characters and contain only letters, numbers, and underscores'),
  body('password')
    .isLength({ min: 8 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must be at least 8 characters with at least one lowercase, uppercase, and number'),
  body('firstName').optional().isLength({ min: 1, max: 50 }).withMessage('First name must be 1-50 characters'),
  body('lastName').optional().isLength({ min: 1, max: 50 }).withMessage('Last name must be 1-50 characters'),
  handleValidationErrors
]

const validateUpdateUser = [
  body('email').optional().isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('username')
    .optional()
    .isLength({ min: 3, max: 30 })
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username must be 3-30 characters and contain only letters, numbers, and underscores'),
  body('firstName').optional().isLength({ min: 1, max: 50 }).withMessage('First name must be 1-50 characters'),
  body('lastName').optional().isLength({ min: 1, max: 50 }).withMessage('Last name must be 1-50 characters'),
  body('isActive').optional().isBoolean().withMessage('isActive must be a boolean'),
  handleValidationErrors
]

// Group validations
const validateCreateGroup = [
  body('name')
    .isLength({ min: 1, max: 100 })
    .matches(/^[a-zA-Z0-9\s_-]+$/)
    .withMessage(
      'Group name must be 1-100 characters and contain only letters, numbers, spaces, underscores, and hyphens'
    ),
  body('description').optional().isLength({ max: 500 }).withMessage('Description must be at most 500 characters'),
  handleValidationErrors
]

const validateUpdateGroup = [
  body('name')
    .optional()
    .isLength({ min: 1, max: 100 })
    .matches(/^[a-zA-Z0-9\s_-]+$/)
    .withMessage(
      'Group name must be 1-100 characters and contain only letters, numbers, spaces, underscores, and hyphens'
    ),
  body('description').optional().isLength({ max: 500 }).withMessage('Description must be at most 500 characters'),
  body('isActive').optional().isBoolean().withMessage('isActive must be a boolean'),
  handleValidationErrors
]

const validateAssignUserToGroup = [
  body('userIds').isArray({ min: 1 }).withMessage('userIds must be a non-empty array'),
  body('userIds.*').isString().notEmpty().withMessage('Each user ID must be a non-empty string'),
  handleValidationErrors
]

// Role validations
const validateCreateRole = [
  body('name')
    .isLength({ min: 1, max: 100 })
    .matches(/^[a-zA-Z0-9\s_-]+$/)
    .withMessage(
      'Role name must be 1-100 characters and contain only letters, numbers, spaces, underscores, and hyphens'
    ),
  body('description').optional().isLength({ max: 500 }).withMessage('Description must be at most 500 characters'),
  handleValidationErrors
]

const validateUpdateRole = [
  body('name')
    .optional()
    .isLength({ min: 1, max: 100 })
    .matches(/^[a-zA-Z0-9\s_-]+$/)
    .withMessage(
      'Role name must be 1-100 characters and contain only letters, numbers, spaces, underscores, and hyphens'
    ),
  body('description').optional().isLength({ max: 500 }).withMessage('Description must be at most 500 characters'),
  body('isActive').optional().isBoolean().withMessage('isActive must be a boolean'),
  handleValidationErrors
]

const validateAssignRoleToGroup = [
  body('roleIds').isArray({ min: 1 }).withMessage('roleIds must be a non-empty array'),
  body('roleIds.*').isString().notEmpty().withMessage('Each role ID must be a non-empty string'),
  handleValidationErrors
]

// Module validations
const validateCreateModule = [
  body('name')
    .isLength({ min: 1, max: 100 })
    .matches(/^[a-zA-Z0-9\s_-]+$/)
    .withMessage(
      'Module name must be 1-100 characters and contain only letters, numbers, spaces, underscores, and hyphens'
    ),
  body('description').optional().isLength({ max: 500 }).withMessage('Description must be at most 500 characters'),
  handleValidationErrors
]

const validateUpdateModule = [
  body('name')
    .optional()
    .isLength({ min: 1, max: 100 })
    .matches(/^[a-zA-Z0-9\s_-]+$/)
    .withMessage(
      'Module name must be 1-100 characters and contain only letters, numbers, spaces, underscores, and hyphens'
    ),
  body('description').optional().isLength({ max: 500 }).withMessage('Description must be at most 500 characters'),
  body('isActive').optional().isBoolean().withMessage('isActive must be a boolean'),
  handleValidationErrors
]

// Permission validations
const validateCreatePermission = [
  body('action')
    .isIn(['create', 'read', 'update', 'delete'])
    .withMessage('Action must be one of: create, read, update, delete'),
  body('moduleId').isString().notEmpty().withMessage('Module ID is required'),
  body('description').optional().isLength({ max: 500 }).withMessage('Description must be at most 500 characters'),
  handleValidationErrors
]

const validateUpdatePermission = [
  body('action')
    .optional()
    .isIn(['create', 'read', 'update', 'delete'])
    .withMessage('Action must be one of: create, read, update, delete'),
  body('moduleId').optional().isString().notEmpty().withMessage('Module ID must be a non-empty string'),
  body('description').optional().isLength({ max: 500 }).withMessage('Description must be at most 500 characters'),
  body('isActive').optional().isBoolean().withMessage('isActive must be a boolean'),
  handleValidationErrors
]

const validateAssignPermissionToRole = [
  body('permissionIds').isArray({ min: 1 }).withMessage('permissionIds must be a non-empty array'),
  body('permissionIds.*').isString().notEmpty().withMessage('Each permission ID must be a non-empty string'),
  handleValidationErrors
]

// Access control validations
const validateSimulateAction = [
  body('module').isString().notEmpty().withMessage('Module name is required'),
  body('action')
    .isIn(['create', 'read', 'update', 'delete'])
    .withMessage('Action must be one of: create, read, update, delete'),
  handleValidationErrors
]

// Generic validations
const validateId = [param('id').isString().notEmpty().withMessage('ID is required'), handleValidationErrors]

const validatePagination = [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  handleValidationErrors
]

export {
  handleValidationErrors,
  validateRegister,
  validateLogin,
  validateCreateUser,
  validateUpdateUser,
  validateCreateGroup,
  validateUpdateGroup,
  validateAssignUserToGroup,
  validateCreateRole,
  validateUpdateRole,
  validateAssignRoleToGroup,
  validateCreateModule,
  validateUpdateModule,
  validateCreatePermission,
  validateUpdatePermission,
  validateAssignPermissionToRole,
  validateSimulateAction,
  validateId,
  validatePagination
}
