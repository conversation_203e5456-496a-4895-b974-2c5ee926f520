import express from 'express'
import { getAllGroups, getGroupById, createGroup, updateGroup, deleteGroup, assignUsersToGroup } from '../controllers/groupController.js'
import { assignRolesToGroup } from '../controllers/roleController.js'
import { validateCreateGroup, validateUpdateGroup, validateAssignUserToGroup, validateAssignRoleToGroup, validateId, validatePagination } from '../middleware/validation.js'
import { authenticateToken } from '../middleware/auth.js'

const router = express.Router()

// All group routes require authentication
router.use(authenticateToken)

// GET /api/groups - Get all groups with pagination
router.get('/', validatePagination, getAllGroups)

// GET /api/groups/:id - Get group by ID
router.get('/:id', validateId, getGroupById)

// POST /api/groups - Create new group
router.post('/', validateCreateGroup, createGroup)

// PUT /api/groups/:id - Update group
router.put('/:id', validateId, validateUpdateGroup, updateGroup)

// DELETE /api/groups/:id - Delete group (soft delete)
router.delete('/:id', validateId, deleteGroup)

// POST /api/groups/:id/users - Assign users to group
router.post('/:id/users', validateId, validateAssignUserToGroup, assignUsersToGroup)

// POST /api/groups/:id/roles - Assign roles to group
router.post('/:id/roles', validateId, validateAssignRoleToGroup, assignRolesToGroup)

export default router
