/**
 * Simple logging utility for the IAM system
 */

const LOG_LEVELS = {
  ERROR: 'ERROR',
  WARN: 'WARN',
  INFO: 'INFO',
  DEBUG: 'DEBUG'
}

const formatMessage = (level, message, meta = {}) => {
  const timestamp = new Date().toISOString()
  const metaString = Object.keys(meta).length > 0 ? ` | ${JSON.stringify(meta)}` : ''
  return `[${timestamp}] ${level}: ${message}${metaString}`
}

const shouldLog = (level) => {
  const currentLevel = process.env.LOG_LEVEL || 'INFO'
  const levels = ['ERROR', 'WARN', 'INFO', 'DEBUG']
  return levels.indexOf(level) <= levels.indexOf(currentLevel)
}

const logger = {
  error: (message, meta = {}) => {
    if (shouldLog(LOG_LEVELS.ERROR)) {
      console.error(formatMessage(LOG_LEVELS.ERROR, message, meta))
    }
  },

  warn: (message, meta = {}) => {
    if (shouldLog(LOG_LEVELS.WARN)) {
      console.warn(formatMessage(LOG_LEVELS.WARN, message, meta))
    }
  },

  info: (message, meta = {}) => {
    if (shouldLog(LOG_LEVELS.INFO)) {
      console.log(formatMessage(LOG_LEVELS.INFO, message, meta))
    }
  },

  debug: (message, meta = {}) => {
    if (shouldLog(LOG_LEVELS.DEBUG)) {
      console.log(formatMessage(LOG_LEVELS.DEBUG, message, meta))
    }
  },

  // Specific logging methods for IAM operations
  auth: (action, userId, success, meta = {}) => {
    logger.info(`AUTH: ${action}`, {
      userId,
      success,
      ...meta
    })
  },

  permission: (action, userId, resource, success, meta = {}) => {
    logger.info(`PERMISSION: ${action}`, {
      userId,
      resource,
      success,
      ...meta
    })
  },

  audit: (action, userId, resource, details = {}) => {
    logger.info(`AUDIT: ${action}`, {
      userId,
      resource,
      timestamp: new Date().toISOString(),
      ...details
    })
  }
}

export default logger
