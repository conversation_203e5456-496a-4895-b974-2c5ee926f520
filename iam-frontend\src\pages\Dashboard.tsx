import React, { useState } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { apiService } from '../services/api'

const Dashboard: React.FC = () => {
  const { user, permissions } = useAuth()
  const [simulationResult, setSimulationResult] = useState<{ allowed: boolean; message: string } | null>(null)
  const [simulationForm, setSimulationForm] = useState<PermissionCheck>({
    module: '',
    action: ''
  })
  const [isSimulating, setIsSimulating] = useState(false)

  const handleSimulateAction = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!simulationForm.module || !simulationForm.action) {
      setSimulationResult({
        allowed: false,
        message: 'Please provide both module and action'
      })
      return
    }

    setIsSimulating(true)
    try {
      const response = await apiService.simulateAction(simulationForm)
      if (response.success && response.data) {
        setSimulationResult({
          allowed: response.data.allowed,
          message: response.data.allowed
            ? `✅ Access granted for ${simulationForm.action} on ${simulationForm.module}`
            : `❌ Access denied for ${simulationForm.action} on ${simulationForm.module}`
        })
      } else {
        setSimulationResult({
          allowed: false,
          message: 'Failed to simulate action'
        })
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error && 'response' in error
          ? (error as { response?: { data?: { message?: string } } }).response?.data?.message
          : 'Error simulating action'
      setSimulationResult({
        allowed: false,
        message: errorMessage || 'Error simulating action'
      })
    } finally {
      setIsSimulating(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setSimulationForm(prev => ({
      ...prev,
      [name]: value
    }))
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-white shadow rounded-lg p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Welcome, {user?.firstName} {user?.lastName}!
        </h1>
        <p className="text-gray-600">Welcome to the Identity and Access Management System dashboard.</p>
      </div>

      {/* User Info */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Your Account Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Username</label>
            <p className="mt-1 text-sm text-gray-900">{user?.username}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Email</label>
            <p className="mt-1 text-sm text-gray-900">{user?.email}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Status</label>
            <p className="mt-1 text-sm">
              <span
                className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  user?.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}
              >
                {user?.isActive ? 'Active' : 'Inactive'}
              </span>
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Member Since</label>
            <p className="mt-1 text-sm text-gray-900">
              {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}
            </p>
          </div>
        </div>
      </div>

      {/* Permissions Section */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Your Permissions</h2>
        {permissions && permissions.permissions && permissions.permissions.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {permissions.permissions.map((permission: { module: string; action: string }, index: number) => (
              <div key={index} className="border border-gray-200 rounded-lg p-3 bg-gray-50">
                <div className="text-sm font-medium text-gray-900">{permission.module}</div>
                <div className="text-sm text-gray-600 capitalize">{permission.action}</div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500">No permissions assigned.</p>
        )}
      </div>

      {/* Permission Simulation */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Test Permission Access</h2>
        <p className="text-sm text-gray-600 mb-4">
          Use this tool to test if you have access to perform specific actions on modules.
        </p>

        <form onSubmit={handleSimulateAction} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="module" className="form-label">
                Module
              </label>
              <input
                type="text"
                id="module"
                name="module"
                className="form-input"
                placeholder="e.g., Users, Groups, Roles"
                value={simulationForm.module}
                onChange={handleInputChange}
                required
              />
            </div>
            <div>
              <label htmlFor="action" className="form-label">
                Action
              </label>
              <input
                type="text"
                id="action"
                name="action"
                className="form-input"
                placeholder="e.g., create, read, update, delete"
                value={simulationForm.action}
                onChange={handleInputChange}
                required
              />
            </div>
          </div>

          <button
            type="submit"
            disabled={isSimulating}
            className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSimulating ? 'Testing...' : 'Test Permission'}
          </button>
        </form>

        {simulationResult && (
          <div
            className={`mt-4 p-4 rounded-md ${
              simulationResult.allowed ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
            }`}
          >
            <p className={`text-sm ${simulationResult.allowed ? 'text-green-700' : 'text-red-700'}`}>
              {simulationResult.message}
            </p>
          </div>
        )}
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                <span className="text-white text-sm font-medium">👥</span>
              </div>
            </div>
            <div className="ml-4">
              <div className="text-sm font-medium text-gray-500">Total Permissions</div>
              <div className="text-2xl font-bold text-gray-900">{permissions?.permissions?.length || 0}</div>
            </div>
          </div>
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                <span className="text-white text-sm font-medium">✅</span>
              </div>
            </div>
            <div className="ml-4">
              <div className="text-sm font-medium text-gray-500">Account Status</div>
              <div className="text-2xl font-bold text-gray-900">{user?.isActive ? 'Active' : 'Inactive'}</div>
            </div>
          </div>
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                <span className="text-white text-sm font-medium">🔐</span>
              </div>
            </div>
            <div className="ml-4">
              <div className="text-sm font-medium text-gray-500">Access Level</div>
              <div className="text-2xl font-bold text-gray-900">
                {permissions?.permissions?.length ? 'Granted' : 'Limited'}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
