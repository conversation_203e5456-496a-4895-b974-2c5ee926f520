import { prisma } from '../config/database.js'

const getAllModules = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1
    const limit = parseInt(req.query.limit) || 10
    const skip = (page - 1) * limit

    const [modules, total] = await Promise.all([
      prisma.module.findMany({
        skip,
        take: limit,
        select: {
          id: true,
          name: true,
          description: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
          permissions: {
            select: {
              id: true,
              action: true,
              description: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      }),
      prisma.module.count()
    ])

    res.json({
      success: true,
      data: {
        modules,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    console.error('Get all modules error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

const getModuleById = async (req, res) => {
  try {
    const { id } = req.params

    const module = await prisma.module.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        description: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        permissions: {
          select: {
            id: true,
            action: true,
            description: true,
            isActive: true,
            rolePermissions: {
              include: {
                role: {
                  select: {
                    id: true,
                    name: true
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!module) {
      return res.status(404).json({
        success: false,
        message: 'Module not found'
      })
    }

    res.json({
      success: true,
      data: { module }
    })
  } catch (error) {
    console.error('Get module by ID error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

const createModule = async (req, res) => {
  try {
    const { name, description } = req.body

    // Check if module already exists
    const existingModule = await prisma.module.findUnique({
      where: { name }
    })

    if (existingModule) {
      return res.status(409).json({
        success: false,
        message: 'Module name already exists'
      })
    }

    // Create module
    const module = await prisma.module.create({
      data: {
        name,
        description
      },
      select: {
        id: true,
        name: true,
        description: true,
        isActive: true,
        createdAt: true
      }
    })

    res.status(201).json({
      success: true,
      message: 'Module created successfully',
      data: { module }
    })
  } catch (error) {
    console.error('Create module error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

const updateModule = async (req, res) => {
  try {
    const { id } = req.params
    const { name, description, isActive } = req.body

    // Check if module exists
    const existingModule = await prisma.module.findUnique({
      where: { id }
    })

    if (!existingModule) {
      return res.status(404).json({
        success: false,
        message: 'Module not found'
      })
    }

    // Check for name conflicts
    if (name) {
      const conflictModule = await prisma.module.findFirst({
        where: {
          AND: [{ id: { not: id } }, { name }]
        }
      })

      if (conflictModule) {
        return res.status(409).json({
          success: false,
          message: 'Module name already exists'
        })
      }
    }

    // Update module
    const module = await prisma.module.update({
      where: { id },
      data: {
        ...(name && { name }),
        ...(description !== undefined && { description }),
        ...(isActive !== undefined && { isActive })
      },
      select: {
        id: true,
        name: true,
        description: true,
        isActive: true,
        createdAt: true,
        updatedAt: true
      }
    })

    res.json({
      success: true,
      message: 'Module updated successfully',
      data: { module }
    })
  } catch (error) {
    console.error('Update module error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

const deleteModule = async (req, res) => {
  try {
    const { id } = req.params

    // Check if module exists
    const existingModule = await prisma.module.findUnique({
      where: { id }
    })

    if (!existingModule) {
      return res.status(404).json({
        success: false,
        message: 'Module not found'
      })
    }

    // Soft delete by setting isActive to false
    await prisma.module.update({
      where: { id },
      data: { isActive: false }
    })

    res.json({
      success: true,
      message: 'Module deleted successfully'
    })
  } catch (error) {
    console.error('Delete module error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

export { 
  getAllModules,
  getModuleById,
  createModule,
  updateModule,
  deleteModule
 }
