import React from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { AuthProvider } from './contexts/AuthContext'
import ProtectedRoute from './components/ProtectedRoute'
import Layout from './components/Layout'
import Login from './pages/Login'
import Register from './pages/Register'
import Dashboard from './pages/Dashboard'
import Users from './pages/Users'
import Groups from './pages/Groups'
import Roles from './pages/Roles'
import Modules from './pages/Modules'
import Permissions from './pages/Permissions'

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* Public routes */}
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />

          {/* Protected routes */}
          <Route
            path="/"
            element={
              <ProtectedRoute>
                <Layout />
              </ProtectedRoute>
            }
          >
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={<Dashboard />} />

            <Route
              path="users"
              element={
                <ProtectedRoute requirePermission={{ module: 'Users', action: 'read' }}>
                  <Users />
                </ProtectedRoute>
              }
            />

            <Route
              path="groups"
              element={
                <ProtectedRoute requirePermission={{ module: 'Groups', action: 'read' }}>
                  <Groups />
                </ProtectedRoute>
              }
            />

            <Route
              path="roles"
              element={
                <ProtectedRoute requirePermission={{ module: 'Roles', action: 'read' }}>
                  <Roles />
                </ProtectedRoute>
              }
            />

            <Route
              path="modules"
              element={
                <ProtectedRoute requirePermission={{ module: 'Modules', action: 'read' }}>
                  <Modules />
                </ProtectedRoute>
              }
            />

            <Route
              path="permissions"
              element={
                <ProtectedRoute requirePermission={{ module: 'Permissions', action: 'read' }}>
                  <Permissions />
                </ProtectedRoute>
              }
            />
          </Route>

          {/* Catch all route */}
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </Router>
    </AuthProvider>
  )
}

export default App
