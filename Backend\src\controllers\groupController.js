import { prisma } from '../config/database.js'

const getAllGroups = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1
    const limit = parseInt(req.query.limit) || 10
    const skip = (page - 1) * limit

    const [groups, total] = await Promise.all([
      prisma.group.findMany({
        skip,
        take: limit,
        select: {
          id: true,
          name: true,
          description: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
          userGroups: {
            include: {
              user: {
                select: {
                  id: true,
                  username: true,
                  email: true
                }
              }
            }
          },
          groupRoles: {
            include: {
              role: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      }),
      prisma.group.count()
    ])

    res.json({
      success: true,
      data: {
        groups,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    console.error('Get all groups error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

const getGroupById = async (req, res) => {
  try {
    const { id } = req.params

    const group = await prisma.group.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        description: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        userGroups: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
                email: true,
                firstName: true,
                lastName: true
              }
            }
          }
        },
        groupRoles: {
          include: {
            role: {
              select: {
                id: true,
                name: true,
                description: true
              }
            }
          }
        }
      }
    })

    if (!group) {
      return res.status(404).json({
        success: false,
        message: 'Group not found'
      })
    }

    res.json({
      success: true,
      data: { group }
    })
  } catch (error) {
    console.error('Get group by ID error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

const createGroup = async (req, res) => {
  try {
    const { name, description } = req.body

    // Check if group already exists
    const existingGroup = await prisma.group.findUnique({
      where: { name }
    })

    if (existingGroup) {
      return res.status(409).json({
        success: false,
        message: 'Group name already exists'
      })
    }

    // Create group
    const group = await prisma.group.create({
      data: {
        name,
        description
      },
      select: {
        id: true,
        name: true,
        description: true,
        isActive: true,
        createdAt: true
      }
    })

    res.status(201).json({
      success: true,
      message: 'Group created successfully',
      data: { group }
    })
  } catch (error) {
    console.error('Create group error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

const updateGroup = async (req, res) => {
  try {
    const { id } = req.params
    const { name, description, isActive } = req.body

    // Check if group exists
    const existingGroup = await prisma.group.findUnique({
      where: { id }
    })

    if (!existingGroup) {
      return res.status(404).json({
        success: false,
        message: 'Group not found'
      })
    }

    // Check for name conflicts
    if (name) {
      const conflictGroup = await prisma.group.findFirst({
        where: {
          AND: [{ id: { not: id } }, { name }]
        }
      })

      if (conflictGroup) {
        return res.status(409).json({
          success: false,
          message: 'Group name already exists'
        })
      }
    }

    // Update group
    const group = await prisma.group.update({
      where: { id },
      data: {
        ...(name && { name }),
        ...(description !== undefined && { description }),
        ...(isActive !== undefined && { isActive })
      },
      select: {
        id: true,
        name: true,
        description: true,
        isActive: true,
        createdAt: true,
        updatedAt: true
      }
    })

    res.json({
      success: true,
      message: 'Group updated successfully',
      data: { group }
    })
  } catch (error) {
    console.error('Update group error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

const deleteGroup = async (req, res) => {
  try {
    const { id } = req.params

    // Check if group exists
    const existingGroup = await prisma.group.findUnique({
      where: { id }
    })

    if (!existingGroup) {
      return res.status(404).json({
        success: false,
        message: 'Group not found'
      })
    }

    // Soft delete by setting isActive to false
    await prisma.group.update({
      where: { id },
      data: { isActive: false }
    })

    res.json({
      success: true,
      message: 'Group deleted successfully'
    })
  } catch (error) {
    console.error('Delete group error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

const assignUsersToGroup = async (req, res) => {
  try {
    const { id: groupId } = req.params
    const { userIds } = req.body

    // Check if group exists
    const group = await prisma.group.findUnique({
      where: { id: groupId, isActive: true }
    })

    if (!group) {
      return res.status(404).json({
        success: false,
        message: 'Group not found'
      })
    }

    // Check if all users exist and are active
    const users = await prisma.user.findMany({
      where: {
        id: { in: userIds },
        isActive: true
      }
    })

    if (users.length !== userIds.length) {
      return res.status(400).json({
        success: false,
        message: 'One or more users not found or inactive'
      })
    }

    // Create user-group assignments (ignore duplicates)
    const assignments = userIds.map(userId => ({
      userId,
      groupId,
      assignedBy: req.user.id
    }))

    await prisma.userGroup.createMany({
      data: assignments,
      skipDuplicates: true
    })

    res.json({
      success: true,
      message: 'Users assigned to group successfully'
    })
  } catch (error) {
    console.error('Assign users to group error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

export { 
  getAllGroups,
  getGroupById,
  createGroup,
  updateGroup,
  deleteGroup,
  assignUsersToGroup
 }
