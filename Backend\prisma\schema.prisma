// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  firstName String?
  lastName  String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  userGroups UserGroup[]

  @@map("users")
}

model Group {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  userGroups UserGroup[]
  groupRoles GroupRole[]

  @@map("groups")
}

model Role {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  groupRoles      GroupRole[]
  rolePermissions RolePermission[]

  @@map("roles")
}

model Module {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  permissions Permission[]

  @@map("modules")
}

model Permission {
  id          String   @id @default(cuid())
  action      String   // create, read, update, delete
  moduleId    String
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  module          Module           @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  rolePermissions RolePermission[]

  @@unique([action, moduleId])
  @@map("permissions")
}

// Junction Tables
model UserGroup {
  id       String @id @default(cuid())
  userId   String
  groupId  String
  assignedAt DateTime @default(now())
  assignedBy String?

  // Relationships
  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  group Group @relation(fields: [groupId], references: [id], onDelete: Cascade)

  @@unique([userId, groupId])
  @@map("user_groups")
}

model GroupRole {
  id       String @id @default(cuid())
  groupId  String
  roleId   String
  assignedAt DateTime @default(now())
  assignedBy String?

  // Relationships
  group Group @relation(fields: [groupId], references: [id], onDelete: Cascade)
  role  Role  @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([groupId, roleId])
  @@map("group_roles")
}

model RolePermission {
  id           String @id @default(cuid())
  roleId       String
  permissionId String
  assignedAt   DateTime @default(now())
  assignedBy   String?

  // Relationships
  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@map("role_permissions")
}
