import { verifyToken, extractTokenFromHeader } from '../utils/jwt.js'
import { checkUserPermission } from '../utils/permissionService.js'
import { prisma } from '../config/database.js'

const authenticateToken = async (req, res, next) => {
  try {
    const token = extractTokenFromHeader(req.headers.authorization)

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access token is required'
      })
    }

    const decoded = verifyToken(token)

    // Verify user still exists and is active
    const user = await prisma.user.findUnique({
      where: {
        id: decoded.userId,
        isActive: true
      },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        isActive: true
      }
    })

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token or user not found'
      })
    }

    req.user = user
    next()
  } catch (error) {
    return res.status(401).json({
      success: false,
      message: 'Invalid token',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

/**
 * Middleware to check if user has permission for a specific module and action
 * @param {string} moduleName - The module name
 * @param {string} action - The action (create, read, update, delete)
 * @returns {Function} Express middleware function
 */
const checkPermission = (moduleName, action) => {
  return async (req, res, next) => {
    try {
      if (!req.user || !req.user.id) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        })
      }

      const hasPermission = await checkUserPermission(req.user.id, moduleName, action)

      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: `Access denied. Required permission: ${action} on ${moduleName}`,
          requiredPermission: {
            module: moduleName,
            action: action
          }
        })
      }

      next()
    } catch (error) {
      console.error('Permission check error:', error)
      return res.status(500).json({
        success: false,
        message: 'Error checking permissions',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      })
    }
  }
}

export {
  authenticateToken,
  checkPermission
}
