# IAM System Backend

A comprehensive Identity and Access Management (IAM) system backend built with Node.js, Express, and Prisma.

## Features

- **Authentication**: JWT-based authentication with secure password hashing
- **User Management**: Complete CRUD operations for users
- **Group Management**: Create and manage user groups
- **Role Management**: Define roles and assign them to groups
- **Module Management**: Define business modules/areas
- **Permission Management**: Fine-grained permissions (CRUD) on modules
- **Access Control**: Permission inheritance through group memberships
- **Security**: Comprehensive input validation and error handling

## Architecture

The system follows a hierarchical permission model:

- **Users** are assigned to **Groups**
- **Groups** have **Roles**
- **Roles** define **Permissions** on **Modules**
- Users inherit permissions only through group memberships

## Tech Stack

- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: SQLite with Prisma ORM
- **Authentication**: JWT (JSON Web Tokens)
- **Password Security**: bcryptjs
- **Validation**: express-validator
- **Security**: helmet, cors

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- No external database required (uses SQLite)

### Installation

1. Clone the repository
2. Install dependencies:

   ```bash
   npm install
   ```

3. Set up environment variables:

   ```bash
   cp .env.example .env
   ```

   Update the `.env` file with your JWT secret (no database configuration needed).

4. Generate Prisma client:

   ```bash
   npm run db:generate
   ```

5. Start the development server (database will be auto-initialized):
   ```bash
   npm run dev
   ```

The server will start on `http://localhost:3000` with a SQLite database

## API Endpoints

### Authentication

- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/profile` - Get current user profile

### Users

- `GET /api/users` - Get all users (paginated)
- `GET /api/users/:id` - Get user by ID
- `POST /api/users` - Create new user
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user (soft delete)

### Groups

- `GET /api/groups` - Get all groups (paginated)
- `GET /api/groups/:id` - Get group by ID
- `POST /api/groups` - Create new group
- `PUT /api/groups/:id` - Update group
- `DELETE /api/groups/:id` - Delete group (soft delete)
- `POST /api/groups/:id/users` - Assign users to group
- `POST /api/groups/:id/roles` - Assign roles to group

### Roles

- `GET /api/roles` - Get all roles (paginated)
- `GET /api/roles/:id` - Get role by ID
- `POST /api/roles` - Create new role
- `PUT /api/roles/:id` - Update role
- `DELETE /api/roles/:id` - Delete role (soft delete)
- `POST /api/roles/:id/permissions` - Assign permissions to role

### Modules

- `GET /api/modules` - Get all modules (paginated)
- `GET /api/modules/:id` - Get module by ID
- `POST /api/modules` - Create new module
- `PUT /api/modules/:id` - Update module
- `DELETE /api/modules/:id` - Delete module (soft delete)

### Permissions

- `GET /api/permissions` - Get all permissions (paginated)
- `GET /api/permissions/:id` - Get permission by ID
- `POST /api/permissions` - Create new permission
- `PUT /api/permissions/:id` - Update permission
- `DELETE /api/permissions/:id` - Delete permission (soft delete)

### Access Control

- `GET /api/me/permissions` - Get current user's inherited permissions
- `POST /api/simulate-action` - Test user's ability to perform an action

## Authentication

All protected routes require a JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Permission System

The system uses a hierarchical permission model where:

1. **Modules** represent business areas (e.g., "Users", "Groups", "Reports")
2. **Permissions** define actions on modules (create, read, update, delete)
3. **Roles** group multiple permissions together
4. **Groups** have roles assigned to them
5. **Users** inherit permissions through group memberships

### Example Permission Flow

1. Create a module: "Users"
2. Create permissions: "create:Users", "read:Users", "update:Users", "delete:Users"
3. Create a role: "User Manager"
4. Assign permissions to the role
5. Create a group: "HR Department"
6. Assign the role to the group
7. Add users to the group
8. Users now inherit all permissions from the role

## Error Handling

The API uses consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "errors": ["Detailed error messages"]
}
```

## Security Features

- Password hashing with bcryptjs
- JWT token authentication
- Input validation and sanitization
- SQL injection prevention (Prisma ORM)
- CORS protection
- Security headers (helmet)
- Rate limiting ready
- Comprehensive logging

## Development

### Available Scripts

- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema to database
- `npm run db:migrate` - Run database migrations
- `npm run db:studio` - Open Prisma Studio

### Project Structure

```
src/
├── config/          # Database configuration
├── controllers/     # Route controllers
├── middleware/      # Express middleware
├── routes/          # API routes
├── utils/           # Utility functions
└── server.js        # Main server file
```

## Environment Variables

```env
# No DATABASE_URL needed for SQLite
JWT_SECRET="your-super-secret-jwt-key"
JWT_EXPIRES_IN="24h"
PORT=3000
NODE_ENV="development"
CORS_ORIGIN="http://localhost:3001"
```

## Testing the API

### 1. Register a User

```bash
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "admin",
    "password": "Admin123!",
    "firstName": "Admin",
    "lastName": "User"
  }'
```

### 2. Login

```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Admin123!"
  }'
```

Save the JWT token from the response for subsequent requests.

### 3. Create a Module

```bash
curl -X POST http://localhost:3000/api/modules \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "Users",
    "description": "User management module"
  }'
```

### 4. Create Permissions

```bash
curl -X POST http://localhost:3000/api/permissions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "action": "create",
    "moduleId": "MODULE_ID_FROM_STEP_3",
    "description": "Create users"
  }'
```

### 5. Test Permission Simulation

```bash
curl -X POST http://localhost:3000/api/simulate-action \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "module": "Users",
    "action": "create"
  }'
```

### 6. Get User Permissions

```bash
curl -X GET http://localhost:3000/api/me/permissions \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Health Check

Check if the API is running:

```bash
curl http://localhost:3000/health
```

## License

This project is licensed under the ISC License.
