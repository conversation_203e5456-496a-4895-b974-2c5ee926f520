import { prisma } from '../config/database.js'

const getAllPermissions = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1
    const limit = parseInt(req.query.limit) || 10
    const skip = (page - 1) * limit

    const [permissions, total] = await Promise.all([
      prisma.permission.findMany({
        skip,
        take: limit,
        select: {
          id: true,
          action: true,
          description: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
          module: {
            select: {
              id: true,
              name: true,
              description: true
            }
          },
          rolePermissions: {
            include: {
              role: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      }),
      prisma.permission.count()
    ])

    res.json({
      success: true,
      data: {
        permissions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    console.error('Get all permissions error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

const getPermissionById = async (req, res) => {
  try {
    const { id } = req.params

    const permission = await prisma.permission.findUnique({
      where: { id },
      select: {
        id: true,
        action: true,
        description: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        module: {
          select: {
            id: true,
            name: true,
            description: true
          }
        },
        rolePermissions: {
          include: {
            role: {
              select: {
                id: true,
                name: true,
                description: true
              }
            }
          }
        }
      }
    })

    if (!permission) {
      return res.status(404).json({
        success: false,
        message: 'Permission not found'
      })
    }

    res.json({
      success: true,
      data: { permission }
    })
  } catch (error) {
    console.error('Get permission by ID error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

const createPermission = async (req, res) => {
  try {
    const { action, moduleId, description } = req.body

    // Check if module exists
    const module = await prisma.module.findUnique({
      where: { id: moduleId, isActive: true }
    })

    if (!module) {
      return res.status(404).json({
        success: false,
        message: 'Module not found'
      })
    }

    // Check if permission already exists for this action and module
    const existingPermission = await prisma.permission.findUnique({
      where: {
        action_moduleId: {
          action,
          moduleId
        }
      }
    })

    if (existingPermission) {
      return res.status(409).json({
        success: false,
        message: 'Permission for this action and module already exists'
      })
    }

    // Create permission
    const permission = await prisma.permission.create({
      data: {
        action,
        moduleId,
        description
      },
      select: {
        id: true,
        action: true,
        description: true,
        isActive: true,
        createdAt: true,
        module: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    res.status(201).json({
      success: true,
      message: 'Permission created successfully',
      data: { permission }
    })
  } catch (error) {
    console.error('Create permission error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

const updatePermission = async (req, res) => {
  try {
    const { id } = req.params
    const { action, moduleId, description, isActive } = req.body

    // Check if permission exists
    const existingPermission = await prisma.permission.findUnique({
      where: { id }
    })

    if (!existingPermission) {
      return res.status(404).json({
        success: false,
        message: 'Permission not found'
      })
    }

    // Check if module exists (if moduleId is being updated)
    if (moduleId) {
      const module = await prisma.module.findUnique({
        where: { id: moduleId, isActive: true }
      })

      if (!module) {
        return res.status(404).json({
          success: false,
          message: 'Module not found'
        })
      }
    }

    // Check for conflicts if action or moduleId is being updated
    if (action || moduleId) {
      const conflictPermission = await prisma.permission.findFirst({
        where: {
          AND: [
            { id: { not: id } },
            {
              action: action || existingPermission.action,
              moduleId: moduleId || existingPermission.moduleId
            }
          ]
        }
      })

      if (conflictPermission) {
        return res.status(409).json({
          success: false,
          message: 'Permission for this action and module already exists'
        })
      }
    }

    // Update permission
    const permission = await prisma.permission.update({
      where: { id },
      data: {
        ...(action && { action }),
        ...(moduleId && { moduleId }),
        ...(description !== undefined && { description }),
        ...(isActive !== undefined && { isActive })
      },
      select: {
        id: true,
        action: true,
        description: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        module: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    res.json({
      success: true,
      message: 'Permission updated successfully',
      data: { permission }
    })
  } catch (error) {
    console.error('Update permission error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

const deletePermission = async (req, res) => {
  try {
    const { id } = req.params

    // Check if permission exists
    const existingPermission = await prisma.permission.findUnique({
      where: { id }
    })

    if (!existingPermission) {
      return res.status(404).json({
        success: false,
        message: 'Permission not found'
      })
    }

    // Soft delete by setting isActive to false
    await prisma.permission.update({
      where: { id },
      data: { isActive: false }
    })

    res.json({
      success: true,
      message: 'Permission deleted successfully'
    })
  } catch (error) {
    console.error('Delete permission error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

const assignPermissionsToRole = async (req, res) => {
  try {
    const { id: roleId } = req.params
    const { permissionIds } = req.body

    // Check if role exists
    const role = await prisma.role.findUnique({
      where: { id: roleId, isActive: true }
    })

    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      })
    }

    // Check if all permissions exist and are active
    const permissions = await prisma.permission.findMany({
      where: {
        id: { in: permissionIds },
        isActive: true
      }
    })

    if (permissions.length !== permissionIds.length) {
      return res.status(400).json({
        success: false,
        message: 'One or more permissions not found or inactive'
      })
    }

    // Create role-permission assignments (ignore duplicates)
    const assignments = permissionIds.map(permissionId => ({
      roleId,
      permissionId,
      assignedBy: req.user.id
    }))

    await prisma.rolePermission.createMany({
      data: assignments,
      skipDuplicates: true
    })

    res.json({
      success: true,
      message: 'Permissions assigned to role successfully'
    })
  } catch (error) {
    console.error('Assign permissions to role error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

export { 
  getAllPermissions,
  getPermissionById,
  createPermission,
  updatePermission,
  deletePermission,
  assignPermissionsToRole
 }
