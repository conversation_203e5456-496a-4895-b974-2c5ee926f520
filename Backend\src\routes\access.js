import express from 'express'
import { getMyPermissions, simulateUserAction } from '../controllers/accessController.js'
import { validateSimulateAction } from '../middleware/validation.js'
import { authenticateToken } from '../middleware/auth.js'

const router = express.Router()

// All access control routes require authentication
router.use(authenticateToken)

// GET /api/me/permissions - Get current user's inherited permissions
router.get('/me/permissions', getMyPermissions)

// POST /api/simulate-action - Test user's ability to perform an action
router.post('/simulate-action', validateSimulateAction, simulateUserAction)

export default router
