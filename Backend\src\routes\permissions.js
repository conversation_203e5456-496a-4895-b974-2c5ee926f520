import express from 'express'
import { getAllPermissions, getPermissionById, createPermission, updatePermission, deletePermission } from '../controllers/permissionController.js'
import { validateCreatePermission, validateUpdatePermission, validateId, validatePagination } from '../middleware/validation.js'
import { authenticateToken } from '../middleware/auth.js'

const router = express.Router()

// All permission routes require authentication
router.use(authenticateToken)

// GET /api/permissions - Get all permissions with pagination
router.get('/', validatePagination, getAllPermissions)

// GET /api/permissions/:id - Get permission by ID
router.get('/:id', validateId, getPermissionById)

// POST /api/permissions - Create new permission
router.post('/', validateCreatePermission, createPermission)

// PUT /api/permissions/:id - Update permission
router.put('/:id', validateId, validateUpdatePermission, updatePermission)

// DELETE /api/permissions/:id - Delete permission (soft delete)
router.delete('/:id', validateId, deletePermission)

export default router
