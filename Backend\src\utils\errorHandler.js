import logger from './logger.js'

/**
 * Custom error classes for better error handling
 */
class AppError extends Error {
  constructor(message, statusCode = 500, isOperational = true) {
    super(message)
    this.statusCode = statusCode
    this.isOperational = isOperational
    this.name = this.constructor.name

    Error.captureStackTrace(this, this.constructor)
  }
}

class ValidationError extends AppError {
  constructor(message, errors = []) {
    super(message, 400)
    this.errors = errors
  }
}

class AuthenticationError extends AppError {
  constructor(message = 'Authentication failed') {
    super(message, 401)
  }
}

class AuthorizationError extends AppError {
  constructor(message = 'Access denied') {
    super(message, 403)
  }
}

class NotFoundError extends AppError {
  constructor(resource = 'Resource') {
    super(`${resource} not found`, 404)
  }
}

class ConflictError extends AppError {
  constructor(message = 'Resource already exists') {
    super(message, 409)
  }
}

/**
 * Async error wrapper to catch errors in async route handlers
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next)
  }
}

/**
 * Global error handler middleware
 */
const globalErrorHandler = (error, req, res, next) => {
  let err = { ...error }
  err.message = error.message

  // Log error
  logger.error('Global error handler', {
    error: err.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    userId: req.user?.id,
    ip: req.ip
  })

  // Prisma errors
  if (error.code === 'P2002') {
    const field = error.meta?.target?.[0] || 'field'
    err = new ConflictError(`${field} already exists`)
  } else if (error.code === 'P2025') {
    err = new NotFoundError()
  } else if (error.code?.startsWith('P')) {
    err = new AppError('Database operation failed', 500)
  }

  // JWT errors
  if (error.name === 'JsonWebTokenError') {
    err = new AuthenticationError('Invalid token')
  } else if (error.name === 'TokenExpiredError') {
    err = new AuthenticationError('Token expired')
  }

  // Validation errors
  if (error.name === 'ValidationError') {
    const errors = Object.values(error.errors).map(val => val.message)
    err = new ValidationError('Validation failed', errors)
  }

  // Default to 500 server error
  if (!err.statusCode) {
    err = new AppError('Internal server error', 500)
  }

  res.status(err.statusCode).json({
    success: false,
    message: err.message,
    ...(err.errors && { errors: err.errors }),
    ...(process.env.NODE_ENV === 'development' && { 
      stack: error.stack,
      details: error 
    })
  })
}

/**
 * 404 handler for undefined routes
 */
const notFoundHandler = (req, res, next) => {
  const error = new NotFoundError(`Route ${req.originalUrl}`)
  next(error)
}

/**
 * Response helper functions
 */
const sendSuccess = (res, data = null, message = 'Success', statusCode = 200) => {
  res.status(statusCode).json({
    success: true,
    message,
    ...(data && { data })
  })
}

const sendError = (res, message = 'Error', statusCode = 500, errors = null) => {
  res.status(statusCode).json({
    success: false,
    message,
    ...(errors && { errors })
  })
}

export {
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  asyncHandler,
  globalErrorHandler,
  notFoundHandler,
  sendSuccess,
  sendError
}
