# IAM System Deployment Guide

## Quick Start

### 1. Prerequisites

- Node.js 16+ installed
- Git (for cloning)
- No external database required (uses SQLite in-memory)

### 2. Setup Steps

1. **Clone and Install**

   ```bash
   cd backend
   npm install
   ```

2. **Environment Configuration**

   ```bash
   cp .env.example .env
   ```

   Update `.env` with your configuration:

   ```env
   # No DATABASE_URL needed for SQLite in-memory
   JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
   JWT_EXPIRES_IN="24h"
   PORT=3000
   NODE_ENV="development"
   CORS_ORIGIN="http://localhost:3001"
   ```

3. **Setup Prisma Client**

   ```bash
   # Generate Prisma client for SQLite
   npm run db:generate
   ```

4. **Start the Server**

   ```bash
   # Development mode (auto-initializes and seeds database)
   npm run dev

   # Production mode
   npm start
   ```

5. **Verify Installation**
   ```bash
   curl http://localhost:3000/health
   ```

## Sample Data

After running `npm run db:seed`, you'll have:

### Test Accounts

- **Admin**: `<EMAIL>` / `Password123!`
- **HR Manager**: `<EMAIL>` / `Password123!`
- **Employee**: `<EMAIL>` / `Password123!`

### Modules Created

- Users (with CRUD permissions)
- Groups (with CRUD permissions)
- Roles (with CRUD permissions)
- Reports (with CRUD permissions)

### Permission Structure

- **Administrator Role**: All permissions on all modules
- **Manager Role**: Limited permissions
- **User Role**: Read-only permissions

## Testing the Complete Flow

### 1. Login as Admin

```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Password123!"
  }'
```

### 2. Check Admin Permissions

```bash
curl -X GET http://localhost:3000/api/me/permissions \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

### 3. Test Permission Simulation

```bash
# Should return true for admin
curl -X POST http://localhost:3000/api/simulate-action \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{
    "module": "Users",
    "action": "create"
  }'
```

### 4. Login as Regular Employee

```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Password123!"
  }'
```

### 5. Test Employee Permissions

```bash
# Should return false for create action
curl -X POST http://localhost:3000/api/simulate-action \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_EMPLOYEE_TOKEN" \
  -d '{
    "module": "Users",
    "action": "create"
  }'

# Should return true for read action
curl -X POST http://localhost:3000/api/simulate-action \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_EMPLOYEE_TOKEN" \
  -d '{
    "module": "Users",
    "action": "read"
  }'
```

## Production Deployment

### Environment Variables

```env
NODE_ENV="production"
DATABASE_URL="your-production-database-url"
JWT_SECRET="your-strong-production-secret"
PORT=3000
CORS_ORIGIN="https://your-frontend-domain.com"
```

### Security Checklist

- [ ] Change default JWT secret
- [ ] Use strong database passwords
- [ ] Enable HTTPS
- [ ] Configure proper CORS origins
- [ ] Set up rate limiting
- [ ] Configure logging
- [ ] Set up monitoring

### Docker Deployment (Optional)

Create `Dockerfile`:

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npx prisma generate
EXPOSE 3000
CMD ["npm", "start"]
```

Create `docker-compose.yml`:

```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - '3000:3000'
    environment:
      - DATABASE_URL=**************************************/iam_system
      - JWT_SECRET=your-secret-key
    depends_on:
      - db

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=iam_system
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

## Troubleshooting

### Common Issues

1. **Database Connection Error**

   - Check DATABASE_URL format
   - Ensure PostgreSQL is running
   - Verify credentials

2. **JWT Token Issues**

   - Check JWT_SECRET is set
   - Verify token format in Authorization header
   - Check token expiration

3. **Permission Denied Errors**

   - Verify user has proper group memberships
   - Check role assignments
   - Ensure permissions are correctly assigned to roles

4. **CORS Errors**
   - Update CORS_ORIGIN in .env
   - Check frontend URL matches

### Logs and Debugging

Enable debug logging:

```env
LOG_LEVEL="DEBUG"
```

Check server logs for detailed error information.

## API Documentation

The complete API documentation is available in the main README.md file.

## Support

For issues and questions:

1. Check the troubleshooting section
2. Review the API documentation
3. Check server logs for error details
