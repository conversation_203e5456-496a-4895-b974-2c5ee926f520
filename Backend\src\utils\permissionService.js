import { prisma } from '../config/database.js'

/**
 * Get all permissions for a user through their group memberships
 * @param {string} userId - The user ID
 * @returns {Promise<Array>} Array of permissions with module and action details
 */
const getUserPermissions = async userId => {
  try {
    const userWithPermissions = await prisma.user.findUnique({
      where: {
        id: userId,
        isActive: true
      },
      select: {
        id: true,
        userGroups: {
          where: {
            group: {
              isActive: true
            }
          },
          select: {
            group: {
              select: {
                id: true,
                name: true,
                groupRoles: {
                  where: {
                    role: {
                      isActive: true
                    }
                  },
                  select: {
                    role: {
                      select: {
                        id: true,
                        name: true,
                        rolePermissions: {
                          where: {
                            permission: {
                              isActive: true
                            }
                          },
                          select: {
                            permission: {
                              select: {
                                id: true,
                                action: true,
                                description: true,
                                module: {
                                  select: {
                                    id: true,
                                    name: true,
                                    description: true
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!userWithPermissions) {
      return []
    }

    // Flatten and deduplicate permissions
    const permissionsMap = new Map()

    userWithPermissions.userGroups.forEach(userGroup => {
      userGroup.group.groupRoles.forEach(groupRole => {
        groupRole.role.rolePermissions.forEach(rolePermission => {
          const permission = rolePermission.permission
          const key = `${permission.module.name}:${permission.action}`

          if (!permissionsMap.has(key)) {
            permissionsMap.set(key, {
              id: permission.id,
              action: permission.action,
              description: permission.description,
              module: permission.module,
              // Additional context for debugging
              inheritedFrom: {
                group: userGroup.group.name,
                role: groupRole.role.name
              }
            })
          }
        })
      })
    })

    return Array.from(permissionsMap.values())
  } catch (error) {
    console.error('Error getting user permissions:', error)
    throw error
  }
}

/**
 * Check if a user has a specific permission
 * @param {string} userId - The user ID
 * @param {string} moduleName - The module name
 * @param {string} action - The action (create, read, update, delete)
 * @returns {Promise<boolean>} True if user has permission, false otherwise
 */
const checkUserPermission = async (userId, moduleName, action) => {
  try {
    const permissions = await getUserPermissions(userId)
    return permissions.some(
      permission =>
        permission.module.name.toLowerCase() === moduleName.toLowerCase() &&
        permission.action.toLowerCase() === action.toLowerCase()
    )
  } catch (error) {
    console.error('Error checking user permission:', error)
    return false
  }
}

/**
 * Get user's groups and roles for context
 * @param {string} userId - The user ID
 * @returns {Promise<Object>} User's groups and roles
 */
const getUserContext = async userId => {
  try {
    const user = await prisma.user.findUnique({
      where: {
        id: userId,
        isActive: true
      },
      select: {
        id: true,
        email: true,
        username: true,
        userGroups: {
          where: {
            group: {
              isActive: true
            }
          },
          select: {
            group: {
              select: {
                id: true,
                name: true,
                description: true,
                groupRoles: {
                  where: {
                    role: {
                      isActive: true
                    }
                  },
                  select: {
                    role: {
                      select: {
                        id: true,
                        name: true,
                        description: true
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!user) {
      return null
    }

    // Flatten groups and roles
    const groups = user.userGroups.map(ug => ug.group)
    const roles = []

    groups.forEach(group => {
      group.groupRoles.forEach(gr => {
        if (!roles.find(role => role.id === gr.role.id)) {
          roles.push(gr.role)
        }
      })
    })

    return {
      user: {
        id: user.id,
        email: user.email,
        username: user.username
      },
      groups,
      roles
    }
  } catch (error) {
    console.error('Error getting user context:', error)
    throw error
  }
}

/**
 * Simulate an action to check if user can perform it
 * @param {string} userId - The user ID
 * @param {string} moduleName - The module name
 * @param {string} action - The action to simulate
 * @returns {Promise<Object>} Simulation result with permission status and context
 */
const simulateAction = async (userId, moduleName, action) => {
  try {
    const hasPermission = await checkUserPermission(userId, moduleName, action)
    const userContext = await getUserContext(userId)
    const userPermissions = await getUserPermissions(userId)

    return {
      hasPermission,
      action,
      module: moduleName,
      user: userContext?.user,
      groups: userContext?.groups || [],
      roles: userContext?.roles || [],
      allPermissions: userPermissions,
      relevantPermissions: userPermissions.filter(p => p.module.name.toLowerCase() === moduleName.toLowerCase())
    }
  } catch (error) {
    console.error('Error simulating action:', error)
    throw error
  }
}

export {
  getUserPermissions,
  checkUserPermission,
  getUserContext,
  simulateAction
}
