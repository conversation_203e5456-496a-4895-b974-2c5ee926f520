import { prisma } from '../config/database.js'

const getAllRoles = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1
    const limit = parseInt(req.query.limit) || 10
    const skip = (page - 1) * limit

    const [roles, total] = await Promise.all([
      prisma.role.findMany({
        skip,
        take: limit,
        select: {
          id: true,
          name: true,
          description: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
          groupRoles: {
            include: {
              group: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          },
          rolePermissions: {
            include: {
              permission: {
                select: {
                  id: true,
                  action: true,
                  module: {
                    select: {
                      id: true,
                      name: true
                    }
                  }
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      }),
      prisma.role.count()
    ])

    res.json({
      success: true,
      data: {
        roles,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    console.error('Get all roles error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

const getRoleById = async (req, res) => {
  try {
    const { id } = req.params

    const role = await prisma.role.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        description: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        groupRoles: {
          include: {
            group: {
              select: {
                id: true,
                name: true,
                description: true
              }
            }
          }
        },
        rolePermissions: {
          include: {
            permission: {
              select: {
                id: true,
                action: true,
                description: true,
                module: {
                  select: {
                    id: true,
                    name: true,
                    description: true
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      })
    }

    res.json({
      success: true,
      data: { role }
    })
  } catch (error) {
    console.error('Get role by ID error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

const createRole = async (req, res) => {
  try {
    const { name, description } = req.body

    // Check if role already exists
    const existingRole = await prisma.role.findUnique({
      where: { name }
    })

    if (existingRole) {
      return res.status(409).json({
        success: false,
        message: 'Role name already exists'
      })
    }

    // Create role
    const role = await prisma.role.create({
      data: {
        name,
        description
      },
      select: {
        id: true,
        name: true,
        description: true,
        isActive: true,
        createdAt: true
      }
    })

    res.status(201).json({
      success: true,
      message: 'Role created successfully',
      data: { role }
    })
  } catch (error) {
    console.error('Create role error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

const updateRole = async (req, res) => {
  try {
    const { id } = req.params
    const { name, description, isActive } = req.body

    // Check if role exists
    const existingRole = await prisma.role.findUnique({
      where: { id }
    })

    if (!existingRole) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      })
    }

    // Check for name conflicts
    if (name) {
      const conflictRole = await prisma.role.findFirst({
        where: {
          AND: [{ id: { not: id } }, { name }]
        }
      })

      if (conflictRole) {
        return res.status(409).json({
          success: false,
          message: 'Role name already exists'
        })
      }
    }

    // Update role
    const role = await prisma.role.update({
      where: { id },
      data: {
        ...(name && { name }),
        ...(description !== undefined && { description }),
        ...(isActive !== undefined && { isActive })
      },
      select: {
        id: true,
        name: true,
        description: true,
        isActive: true,
        createdAt: true,
        updatedAt: true
      }
    })

    res.json({
      success: true,
      message: 'Role updated successfully',
      data: { role }
    })
  } catch (error) {
    console.error('Update role error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

const deleteRole = async (req, res) => {
  try {
    const { id } = req.params

    // Check if role exists
    const existingRole = await prisma.role.findUnique({
      where: { id }
    })

    if (!existingRole) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      })
    }

    // Soft delete by setting isActive to false
    await prisma.role.update({
      where: { id },
      data: { isActive: false }
    })

    res.json({
      success: true,
      message: 'Role deleted successfully'
    })
  } catch (error) {
    console.error('Delete role error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

const assignRolesToGroup = async (req, res) => {
  try {
    const { id: groupId } = req.params
    const { roleIds } = req.body

    // Check if group exists
    const group = await prisma.group.findUnique({
      where: { id: groupId, isActive: true }
    })

    if (!group) {
      return res.status(404).json({
        success: false,
        message: 'Group not found'
      })
    }

    // Check if all roles exist and are active
    const roles = await prisma.role.findMany({
      where: {
        id: { in: roleIds },
        isActive: true
      }
    })

    if (roles.length !== roleIds.length) {
      return res.status(400).json({
        success: false,
        message: 'One or more roles not found or inactive'
      })
    }

    // Create group-role assignments (ignore duplicates)
    const assignments = roleIds.map(roleId => ({
      groupId,
      roleId,
      assignedBy: req.user.id
    }))

    await prisma.groupRole.createMany({
      data: assignments,
      skipDuplicates: true
    })

    res.json({
      success: true,
      message: 'Roles assigned to group successfully'
    })
  } catch (error) {
    console.error('Assign roles to group error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

export { 
  getAllRoles,
  getRoleById,
  createRole,
  updateRole,
  deleteRole,
  assignRolesToGroup
 }
