# IAM System Frontend

A modern React TypeScript frontend for the Identity and Access Management (IAM) System.

## Features

### Authentication & Authorization

- JWT-based authentication
- Protected routes with permission checks
- Automatic token refresh and logout
- Role-based access control (RBAC)

### User Management

- Complete CRUD operations for users
- User profile management
- Account status management
- Form validation and error handling

### Group Management

- Create, edit, and delete groups
- Assign users to groups
- Visual group membership display
- Bulk user assignment interface

### Role Management

- Role creation and management
- Assign roles to groups
- Visual role assignment interface
- Permission inheritance visualization

### Module Management

- Business area module management
- Module-based permission organization
- CRUD operations with validation

### Permission Management

- Fine-grained permission control
- Action-based permissions (CRUD)
- Assign permissions to roles
- Permission simulation and testing

### Dashboard & Analytics

- User permission overview
- Permission simulation tool
- Account information display
- Quick statistics and metrics

## Technology Stack

- **React 19** - Modern React with hooks
- **TypeScript** - Type-safe development
- **Vite** - Fast build tool and dev server
- **Tailwind CSS** - Utility-first CSS framework
- **React Router** - Client-side routing
- **Axios** - HTTP client for API calls

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── Layout.tsx      # Main application layout
│   ├── Modal.tsx       # Modal component
│   ├── LoadingSpinner.tsx
│   └── ProtectedRoute.tsx
├── contexts/           # React contexts
│   └── AuthContext.tsx # Authentication context
├── hooks/              # Custom React hooks
├── pages/              # Page components
│   ├── Login.tsx
│   ├── Register.tsx
│   ├── Dashboard.tsx
│   ├── Users.tsx
│   ├── Groups.tsx
│   ├── Roles.tsx
│   ├── Modules.tsx
│   └── Permissions.tsx
├── services/           # API services
│   └── api.ts         # API client and endpoints
├── types/              # TypeScript type definitions
│   └── index.ts
├── utils/              # Utility functions
└── App.tsx            # Main application component
```

## Getting Started

### Prerequisites

- Node.js 20.18.0 or higher
- npm or yarn package manager

### Installation

1. Navigate to the frontend directory:

```bash
cd iam-frontend
```

2. Install dependencies:

```bash
npm install
```

3. Start the development server:

```bash
npm run dev
```

The application will be available at `http://localhost:3001`

### Build for Production

```bash
npm run build
```

## API Integration

The frontend communicates with the backend API running on `http://localhost:3000`. The API client includes:

- Automatic JWT token management
- Request/response interceptors
- Error handling and retry logic
- Type-safe API calls

### API Endpoints

- **Authentication**: `/api/auth/login`, `/api/auth/register`
- **Users**: `/api/users` (CRUD operations)
- **Groups**: `/api/groups` (CRUD + user assignment)
- **Roles**: `/api/roles` (CRUD + group assignment)
- **Modules**: `/api/modules` (CRUD operations)
- **Permissions**: `/api/permissions` (CRUD + role assignment)
- **Access Control**: `/api/me/permissions`, `/api/simulate-action`

## Key Features

### Authentication Flow

1. User logs in with username/password
2. JWT token stored in localStorage
3. Token automatically included in API requests
4. Automatic logout on token expiration

### Permission System

- Users inherit permissions through group memberships
- Groups have roles assigned to them
- Roles contain specific permissions
- Permissions are action-based (create, read, update, delete) on modules

### Responsive Design

- Mobile-first responsive layout
- Collapsible sidebar navigation
- Touch-friendly interface
- Optimized for all screen sizes

### User Experience

- Loading states for all operations
- Form validation with real-time feedback
- Confirmation dialogs for destructive actions
- Error messages with dismiss functionality
- Success notifications

## Development Guidelines

### Code Style

- TypeScript strict mode enabled
- ESLint configuration for code quality
- Consistent naming conventions
- Component-based architecture

### State Management

- React Context for global state (authentication)
- Local component state for UI state
- Custom hooks for reusable logic

### Error Handling

- Try-catch blocks for all async operations
- User-friendly error messages
- Graceful degradation for failed requests
- Automatic error recovery where possible

## Security Features

- JWT token-based authentication
- Automatic token expiration handling
- Protected routes with permission checks
- XSS protection through React's built-in sanitization
- CSRF protection through SameSite cookies

## Performance Optimizations

- Code splitting with React.lazy (ready for implementation)
- Optimized bundle size with Vite
- Efficient re-rendering with React hooks
- Lazy loading of components
- Optimized API calls with proper caching

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Contributing

1. Follow the existing code style and patterns
2. Add TypeScript types for all new code
3. Include proper error handling
4. Test all functionality thoroughly
5. Update documentation as needed

## License

This project is part of the IAM System and follows the same licensing terms.
