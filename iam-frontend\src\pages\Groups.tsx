import React, { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { apiService } from '../services/api'

import Modal from '../components/Modal'
import LoadingSpinner from '../components/LoadingSpinner'

const Groups: React.FC = () => {
  const [groups, setGroups] = useState<Group[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isUserModalOpen, setIsUserModalOpen] = useState(false)
  const [editingGroup, setEditingGroup] = useState<Group | null>(null)
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null)
  const [formData, setFormData] = useState<CreateGroupRequest>({
    name: '',
    description: ''
  })
  const [formErrors, setFormErrors] = useState<FormErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedUserIds, setSelectedUserIds] = useState<string[]>([])

  const { hasPermission } = useAuth()

  const canCreate = hasPermission('Groups', 'create')
  const canUpdate = hasPermission('Groups', 'update')
  const canDelete = hasPermission('Groups', 'delete')

  useEffect(() => {
    fetchGroups()
    fetchUsers()
  }, [])

  const fetchGroups = async () => {
    try {
      setLoading(true)
      const response = await apiService.getGroups()
      if (response.success && response.data) {
        setGroups(response.data)
      } else {
        setError(response.message || 'Failed to fetch groups')
      }
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && 'response' in err
          ? (err as { response?: { data?: { message?: string } } }).response?.data?.message
          : 'Failed to fetch groups'
      setError(errorMessage || 'Failed to fetch groups')
    } finally {
      setLoading(false)
    }
  }

  const fetchUsers = async () => {
    try {
      const response = await apiService.getUsers()
      if (response.success && response.data) {
        setUsers(response.data)
      }
    } catch (err: unknown) {
      console.error('Failed to fetch users:', err)
    }
  }

  const handleOpenModal = (group?: Group) => {
    if (group) {
      setEditingGroup(group)
      setFormData({
        name: group.name,
        description: group.description
      })
    } else {
      setEditingGroup(null)
      setFormData({
        name: '',
        description: ''
      })
    }
    setFormErrors({})
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setEditingGroup(null)
    setFormData({
      name: '',
      description: ''
    })
    setFormErrors({})
  }

  const handleOpenUserModal = (group: Group) => {
    setSelectedGroup(group)
    // Pre-select users that are already in the group
    const groupUserIds = group.users?.map(user => user.id) || []
    setSelectedUserIds(groupUserIds)
    setIsUserModalOpen(true)
  }

  const handleCloseUserModal = () => {
    setIsUserModalOpen(false)
    setSelectedGroup(null)
    setSelectedUserIds([])
  }

  const validateForm = (): boolean => {
    const errors: FormErrors = {}

    if (!formData.name.trim()) {
      errors.name = 'Group name is required'
    } else if (formData.name.length < 2) {
      errors.name = 'Group name must be at least 2 characters'
    }

    if (!formData.description.trim()) {
      errors.description = 'Description is required'
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))

    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    try {
      if (editingGroup) {
        // Update group
        const response = await apiService.updateGroup(editingGroup.id, formData)
        if (response.success) {
          await fetchGroups()
          handleCloseModal()
        } else {
          setError(response.message || 'Failed to update group')
        }
      } else {
        // Create group
        const response = await apiService.createGroup(formData)
        if (response.success) {
          await fetchGroups()
          handleCloseModal()
        } else {
          setError(response.message || 'Failed to create group')
        }
      }
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && 'response' in err
          ? (err as { response?: { data?: { message?: string } } }).response?.data?.message
          : 'Failed to save group'
      setError(errorMessage || 'Failed to save group')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDelete = async (group: Group) => {
    if (!window.confirm(`Are you sure you want to delete group "${group.name}"?`)) {
      return
    }

    try {
      const response = await apiService.deleteGroup(group.id)
      if (response.success) {
        await fetchGroups()
      } else {
        setError(response.message || 'Failed to delete group')
      }
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && 'response' in err
          ? (err as { response?: { data?: { message?: string } } }).response?.data?.message
          : 'Failed to delete group'
      setError(errorMessage || 'Failed to delete group')
    }
  }

  const handleUserSelection = (userId: string) => {
    setSelectedUserIds(prev => {
      if (prev.includes(userId)) {
        return prev.filter(id => id !== userId)
      } else {
        return [...prev, userId]
      }
    })
  }

  const handleAssignUsers = async () => {
    if (!selectedGroup) return

    setIsSubmitting(true)
    try {
      const response = await apiService.assignUsersToGroup(selectedGroup.id, {
        userIds: selectedUserIds
      })
      if (response.success) {
        await fetchGroups()
        handleCloseUserModal()
      } else {
        setError(response.message || 'Failed to assign users to group')
      }
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && 'response' in err
          ? (err as { response?: { data?: { message?: string } } }).response?.data?.message
          : 'Failed to assign users to group'
      setError(errorMessage || 'Failed to assign users to group')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleRemoveUserFromGroup = async (groupId: string, userId: string) => {
    if (!window.confirm('Are you sure you want to remove this user from the group?')) {
      return
    }

    try {
      const response = await apiService.removeUserFromGroup(groupId, userId)
      if (response.success) {
        await fetchGroups()
      } else {
        setError(response.message || 'Failed to remove user from group')
      }
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && 'response' in err
          ? (err as { response?: { data?: { message?: string } } }).response?.data?.message
          : 'Failed to remove user from group'
      setError(errorMessage || 'Failed to remove user from group')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Groups Management</h1>
        {canCreate && (
          <button onClick={() => handleOpenModal()} className="btn-primary">
            Add Group
          </button>
        )}
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-sm text-red-700">{error}</div>
          <button onClick={() => setError('')} className="mt-2 text-sm text-red-600 hover:text-red-800">
            Dismiss
          </button>
        </div>
      )}

      {/* Groups table */}
      <div className="card p-0">
        <div className="table-container">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th className="table-header-cell">Name</th>
                <th className="table-header-cell">Description</th>
                <th className="table-header-cell">Users</th>
                <th className="table-header-cell">Created</th>
                <th className="table-header-cell">Actions</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {groups.map(group => (
                <tr key={group.id}>
                  <td className="table-cell">
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center mr-3">
                        <span className="text-white text-sm font-medium">{group.name[0].toUpperCase()}</span>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">{group.name}</div>
                      </div>
                    </div>
                  </td>
                  <td className="table-cell">
                    <div className="text-sm text-gray-900 max-w-xs truncate">{group.description}</div>
                  </td>
                  <td className="table-cell">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-900">{group.users?.length || 0} users</span>
                      {group.users && group.users.length > 0 && (
                        <div className="flex -space-x-1">
                          {group.users.slice(0, 3).map(user => (
                            <div
                              key={user.id}
                              className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center border-2 border-white"
                              title={`${user.firstName} ${user.lastName}`}
                            >
                              <span className="text-white text-xs font-medium">
                                {user.firstName[0]}
                                {user.lastName[0]}
                              </span>
                            </div>
                          ))}
                          {group.users.length > 3 && (
                            <div className="w-6 h-6 bg-gray-500 rounded-full flex items-center justify-center border-2 border-white">
                              <span className="text-white text-xs font-medium">+{group.users.length - 3}</span>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="table-cell">{new Date(group.createdAt).toLocaleDateString()}</td>
                  <td className="table-cell">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleOpenUserModal(group)}
                        className="text-green-600 hover:text-green-900 text-sm"
                      >
                        Manage Users
                      </button>
                      {canUpdate && (
                        <button
                          onClick={() => handleOpenModal(group)}
                          className="text-blue-600 hover:text-blue-900 text-sm"
                        >
                          Edit
                        </button>
                      )}
                      {canDelete && (
                        <button onClick={() => handleDelete(group)} className="text-red-600 hover:text-red-900 text-sm">
                          Delete
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {groups.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500">No groups found.</p>
            </div>
          )}
        </div>
      </div>

      {/* Group form modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        title={editingGroup ? 'Edit Group' : 'Add Group'}
        size="md"
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="name" className="form-label">
              Group Name
            </label>
            <input
              type="text"
              id="name"
              name="name"
              className={`form-input ${
                formErrors.name ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
              }`}
              value={formData.name}
              onChange={handleInputChange}
              required
            />
            {formErrors.name && <p className="mt-1 text-sm text-red-600">{formErrors.name}</p>}
          </div>

          <div>
            <label htmlFor="description" className="form-label">
              Description
            </label>
            <textarea
              id="description"
              name="description"
              rows={3}
              className={`form-input ${
                formErrors.description ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
              }`}
              value={formData.description}
              onChange={handleInputChange}
              required
            />
            {formErrors.description && <p className="mt-1 text-sm text-red-600">{formErrors.description}</p>}
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button type="button" onClick={handleCloseModal} className="btn-secondary">
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  {editingGroup ? 'Updating...' : 'Creating...'}
                </>
              ) : editingGroup ? (
                'Update Group'
              ) : (
                'Create Group'
              )}
            </button>
          </div>
        </form>
      </Modal>

      {/* User assignment modal */}
      <Modal
        isOpen={isUserModalOpen}
        onClose={handleCloseUserModal}
        title={`Manage Users - ${selectedGroup?.name}`}
        size="lg"
      >
        <div className="space-y-4">
          <p className="text-sm text-gray-600">
            Select users to assign to this group. Users already in the group are pre-selected.
          </p>

          {/* Current group members */}
          {selectedGroup?.users && selectedGroup.users.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-2">Current Members</h4>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {selectedGroup.users.map(user => (
                  <div key={user.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <div className="flex items-center">
                      <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center mr-2">
                        <span className="text-white text-xs font-medium">
                          {user.firstName[0]}
                          {user.lastName[0]}
                        </span>
                      </div>
                      <span className="text-sm text-gray-900">
                        {user.firstName} {user.lastName} ({user.username})
                      </span>
                    </div>
                    <button
                      onClick={() => handleRemoveUserFromGroup(selectedGroup.id, user.id)}
                      className="text-red-600 hover:text-red-800 text-xs"
                    >
                      Remove
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Available users */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">Available Users</h4>
            <div className="space-y-2 max-h-64 overflow-y-auto border border-gray-200 rounded p-2">
              {users.map(user => (
                <label key={user.id} className="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedUserIds.includes(user.id)}
                    onChange={() => handleUserSelection(user.id)}
                    className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <div className="flex items-center">
                    <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center mr-2">
                      <span className="text-white text-xs font-medium">
                        {user.firstName[0]}
                        {user.lastName[0]}
                      </span>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {user.firstName} {user.lastName}
                      </div>
                      <div className="text-xs text-gray-500">
                        {user.username} • {user.email}
                      </div>
                    </div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button type="button" onClick={handleCloseUserModal} className="btn-secondary">
              Cancel
            </button>
            <button
              onClick={handleAssignUsers}
              disabled={isSubmitting}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  Updating...
                </>
              ) : (
                'Update Group Members'
              )}
            </button>
          </div>
        </div>
      </Modal>
    </div>
  )
}

export default Groups
